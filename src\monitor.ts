/**
 * MySQL MCP服务器监控模块
 * 提供连接池监控、性能统计和系统监控功能
 */

import mysql from 'mysql2/promise';
import { EventEmitter } from 'events';

// ==================== 类型定义 ====================

/**
 * 连接池状态接口
 */
export interface PoolStatus {
  totalConnections: number;    // 总连接数
  activeConnections: number;   // 活跃连接数
  idleConnections: number;     // 空闲连接数
  waitingConnections: number;  // 等待连接数
  maxConnections: number;      // 最大连接数
  connectionLimit: number;     // 连接限制
  acquireTimeout: number;      // 获取超时
  timeout: number;            // 超时时间
  createTimeout: number;       // 创建超时
  destroyTimeout: number;      // 销毁超时
  idleTimeout: number;         // 空闲超时
  reapInterval: number;        // 清理间隔
  createRetryInterval: number; // 创建重试间隔
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  totalQueries: number;        // 总查询数
  successfulQueries: number;   // 成功查询数
  failedQueries: number;       // 失败查询数
  averageQueryTime: number;    // 平均查询时间
  slowQueries: number;         // 慢查询数
  slowQueryThreshold: number;  // 慢查询阈值
  peakConnections: number;     // 峰值连接数
  totalConnectionsCreated: number; // 总创建连接数
  totalConnectionsDestroyed: number; // 总销毁连接数
  memoryUsage: number;         // 内存使用量
  cpuUsage: number;           // CPU使用率
}

/**
 * 监控配置接口
 */
export interface MonitorConfig {
  enabled: boolean;            // 是否启用监控
  interval: number;            // 监控间隔（毫秒）
  slowQueryThreshold: number;  // 慢查询阈值（毫秒）
  maxHistorySize: number;      // 最大历史记录数
  enableMetrics: boolean;      // 是否启用指标收集
  enableAlerts: boolean;       // 是否启用告警
}

/**
 * 监控事件接口
 */
export interface MonitorEvent {
  type: 'connection' | 'query' | 'error' | 'slow_query' | 'pool_full';
  timestamp: number;
  data: any;
}

// ==================== 默认配置 ====================

/**
 * 默认监控配置
 */
export const DEFAULT_MONITOR_CONFIG: MonitorConfig = {
  enabled: true,
  interval: 5000,              // 5秒
  slowQueryThreshold: 1000,    // 1秒
  maxHistorySize: 1000,        // 1000条记录
  enableMetrics: true,
  enableAlerts: true,
};

// ==================== 监控器类 ====================

/**
 * 连接池监控器
 * 提供连接池状态监控、性能统计和告警功能
 */
export class PoolMonitor extends EventEmitter {
  private pool: mysql.Pool;
  private config: MonitorConfig;
  private metrics: PerformanceMetrics;
  private history: MonitorEvent[] = [];
  private monitoringTimer: NodeJS.Timeout | null = null;
  private startTime: number;

  constructor(pool: mysql.Pool, config: Partial<MonitorConfig> = {}) {
    super();
    this.pool = pool;
    this.config = { ...DEFAULT_MONITOR_CONFIG, ...config };
    this.metrics = this.initializeMetrics();
    this.startTime = Date.now();
    
    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      totalQueries: 0,
      successfulQueries: 0,
      failedQueries: 0,
      averageQueryTime: 0,
      slowQueries: 0,
      slowQueryThreshold: this.config.slowQueryThreshold,
      peakConnections: 0,
      totalConnectionsCreated: 0,
      totalConnectionsDestroyed: 0,
      memoryUsage: 0,
      cpuUsage: 0,
    };
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.interval);
  }

  /**
   * 停止监控
   */
  private stopMonitoring(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }
  }

  /**
   * 收集指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      // 获取连接池状态
      const poolStatus = await this.getPoolStatus();
      
      // 更新峰值连接数
      if (poolStatus.activeConnections > this.metrics.peakConnections) {
        this.metrics.peakConnections = poolStatus.activeConnections;
      }

      // 更新内存使用量
      this.metrics.memoryUsage = process.memoryUsage().heapUsed;

      // 检查连接池是否满载
      if (poolStatus.activeConnections >= poolStatus.connectionLimit) {
        this.emitAlert('pool_full', {
          activeConnections: poolStatus.activeConnections,
          connectionLimit: poolStatus.connectionLimit,
        });
      }

      // 检查内存使用
      const memoryUsageMB = this.metrics.memoryUsage / 1024 / 1024;
      if (memoryUsageMB > 500) { // 500MB警告
        this.emitAlert('high_memory', {
          memoryUsage: memoryUsageMB,
          threshold: 500,
        });
      }

    } catch (error) {
      this.emit('error', error);
    }
  }

  /**
   * 获取连接池状态
   */
  async getPoolStatus(): Promise<PoolStatus> {
    // 注意：mysql2的Pool没有直接的状态API，这里提供估算值
    const poolConfig = (this.pool as any).config;
    
    return {
      totalConnections: 0, // 需要从池内部获取
      activeConnections: 0, // 需要从池内部获取
      idleConnections: 0,   // 需要从池内部获取
      waitingConnections: 0, // 需要从池内部获取
      maxConnections: poolConfig.connectionLimit || 10,
      connectionLimit: poolConfig.connectionLimit || 10,
      acquireTimeout: poolConfig.acquireTimeout || 60000,
      timeout: poolConfig.timeout || 60000,
      createTimeout: poolConfig.createTimeout || 60000,
      destroyTimeout: poolConfig.destroyTimeout || 5000,
      idleTimeout: poolConfig.idleTimeout || 60000,
      reapInterval: poolConfig.reapInterval || 1000,
      createRetryInterval: poolConfig.createRetryInterval || 200,
    };
  }

  /**
   * 记录查询事件
   */
  recordQuery(query: string, params: any[], duration: number, success: boolean): void {
    this.metrics.totalQueries++;
    
    if (success) {
      this.metrics.successfulQueries++;
    } else {
      this.metrics.failedQueries++;
    }

    // 更新平均查询时间
    const totalTime = this.metrics.averageQueryTime * (this.metrics.totalQueries - 1) + duration;
    this.metrics.averageQueryTime = totalTime / this.metrics.totalQueries;

    // 检查慢查询
    if (duration > this.config.slowQueryThreshold) {
      this.metrics.slowQueries++;
      this.emitAlert('slow_query', {
        query,
        duration,
        threshold: this.config.slowQueryThreshold,
      });
    }

    // 记录事件
    this.recordEvent('query', {
      query,
      params,
      duration,
      success,
    });
  }

  /**
   * 记录连接事件
   */
  recordConnectionEvent(type: 'created' | 'destroyed'): void {
    if (type === 'created') {
      this.metrics.totalConnectionsCreated++;
    } else {
      this.metrics.totalConnectionsDestroyed++;
    }

    this.recordEvent('connection', { type });
  }

  /**
   * 记录错误事件
   */
  recordError(error: Error, context?: any): void {
    this.recordEvent('error', {
      message: error.message,
      stack: error.stack,
      context,
    });

    if (this.config.enableAlerts) {
      this.emitAlert('error', {
        message: error.message,
        context,
      });
    }
  }

  /**
   * 记录事件
   */
  private recordEvent(type: MonitorEvent['type'], data: any): void {
    const event: MonitorEvent = {
      type,
      timestamp: Date.now(),
      data,
    };

    this.history.push(event);

    // 限制历史记录大小
    if (this.history.length > this.config.maxHistorySize) {
      this.history.shift();
    }

    this.emit('event', event);
  }

  /**
   * 发出告警
   */
  private emitAlert(type: string, data: any): void {
    if (this.config.enableAlerts) {
      this.emit('alert', {
        type,
        timestamp: Date.now(),
        data,
      });
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取历史事件
   */
  getHistory(limit: number = 100): MonitorEvent[] {
    return this.history.slice(-limit);
  }

  /**
   * 获取事件统计
   */
  getEventStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    for (const event of this.history) {
      stats[event.type] = (stats[event.type] || 0) + 1;
    }
    
    return stats;
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.history = [];
    this.startTime = Date.now();
  }

  /**
   * 获取运行时间
   */
  getUptime(): number {
    return Date.now() - this.startTime;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.enabled !== undefined) {
      if (newConfig.enabled && !this.monitoringTimer) {
        this.startMonitoring();
      } else if (!newConfig.enabled && this.monitoringTimer) {
        this.stopMonitoring();
      }
    }
  }

  /**
   * 获取配置
   */
  getConfig(): MonitorConfig {
    return { ...this.config };
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    this.stopMonitoring();
    this.removeAllListeners();
  }
}

// ==================== 性能监控器类 ====================

/**
 * 性能监控器
 * 提供系统级性能监控
 */
export class PerformanceMonitor {
  private metrics: {
    cpuUsage: number[];
    memoryUsage: number[];
    timestamp: number[];
  };

  constructor() {
    this.metrics = {
      cpuUsage: [],
      memoryUsage: [],
      timestamp: [],
    };
  }

  /**
   * 记录性能指标
   */
  recordMetrics(): void {
    const timestamp = Date.now();
    const memoryUsage = process.memoryUsage();
    
    // 计算CPU使用率（简化版本）
    const cpuUsage = this.calculateCPUUsage();
    
    this.metrics.cpuUsage.push(cpuUsage);
    this.metrics.memoryUsage.push(memoryUsage.heapUsed);
    this.metrics.timestamp.push(timestamp);

    // 保持最近100个数据点
    if (this.metrics.cpuUsage.length > 100) {
      this.metrics.cpuUsage.shift();
      this.metrics.memoryUsage.shift();
      this.metrics.timestamp.shift();
    }
  }

  /**
   * 计算CPU使用率（简化版本）
   */
  private calculateCPUUsage(): number {
    const usage = process.cpuUsage();
    return (usage.user + usage.system) / 1000000; // 转换为秒
  }

  /**
   * 获取平均CPU使用率
   */
  getAverageCPUUsage(): number {
    if (this.metrics.cpuUsage.length === 0) return 0;
    const sum = this.metrics.cpuUsage.reduce((a, b) => a + b, 0);
    return sum / this.metrics.cpuUsage.length;
  }

  /**
   * 获取平均内存使用率
   */
  getAverageMemoryUsage(): number {
    if (this.metrics.memoryUsage.length === 0) return 0;
    const sum = this.metrics.memoryUsage.reduce((a, b) => a + b, 0);
    return sum / this.metrics.memoryUsage.length;
  }

  /**
   * 获取性能趋势
   */
  getPerformanceTrend(): {
    cpuTrend: 'increasing' | 'decreasing' | 'stable';
    memoryTrend: 'increasing' | 'decreasing' | 'stable';
  } {
    const cpuUsage = this.metrics.cpuUsage;
    const memoryUsage = this.metrics.memoryUsage;

    if (cpuUsage.length < 2) {
      return { cpuTrend: 'stable', memoryTrend: 'stable' };
    }

    const recentCPU = cpuUsage.slice(-5);
    const recentMemory = memoryUsage.slice(-5);

    const cpuTrend = this.calculateTrend(recentCPU);
    const memoryTrend = this.calculateTrend(recentMemory);

    return { cpuTrend, memoryTrend };
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

    const diff = secondAvg - firstAvg;
    const threshold = firstAvg * 0.1; // 10%阈值

    if (diff > threshold) return 'increasing';
    if (diff < -threshold) return 'decreasing';
    return 'stable';
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    currentCPU: number;
    currentMemory: number;
    averageCPU: number;
    averageMemory: number;
    trend: {
      cpuTrend: 'increasing' | 'decreasing' | 'stable';
      memoryTrend: 'increasing' | 'decreasing' | 'stable';
    };
  } {
    const currentCPU = this.metrics.cpuUsage[this.metrics.cpuUsage.length - 1] || 0;
    const currentMemory = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1] || 0;
    const averageCPU = this.getAverageCPUUsage();
    const averageMemory = this.getAverageMemoryUsage();
    const trend = this.getPerformanceTrend();

    return {
      currentCPU,
      currentMemory,
      averageCPU,
      averageMemory,
      trend,
    };
  }
}

// ==================== 工具函数 ====================

/**
 * 格式化内存大小
 */
export function formatMemorySize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化时间
 */
export function formatUptime(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
} 