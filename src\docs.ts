/**
 * MySQL MCP服务器API文档生成器
 * 自动生成API文档和工具说明
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { TOOLS } from './tools.js';
import { generatePluginDocs } from './plugins.js';
import type { Plugin } from './plugins.js';

// ==================== 类型定义 ====================

/**
 * 文档配置接口
 */
export interface DocConfig {
  title: string;
  version: string;
  description: string;
  author: string;
  outputFormat: 'markdown' | 'html' | 'json';
  includeExamples: boolean;
  includeSchemas: boolean;
  includeChangelog: boolean;
}

/**
 * 工具文档接口
 */
export interface ToolDoc {
  name: string;
  description: string;
  inputSchema: any;
  examples: ToolExample[];
  category: string;
  tags: string[];
}

/**
 * 工具示例接口
 */
export interface ToolExample {
  title: string;
  description: string;
  input: any;
  output: any;
}

// ==================== 默认配置 ====================

/**
 * 默认文档配置
 */
export const DEFAULT_DOC_CONFIG: DocConfig = {
  title: 'MySQL MCP Server API Documentation',
  version: '1.0.0',
  description: 'Complete API documentation for MySQL MCP Server',
  author: 'MySQL MCP Team',
  outputFormat: 'markdown',
  includeExamples: true,
  includeSchemas: true,
  includeChangelog: true,
};

// ==================== 文档生成器类 ====================

/**
 * API文档生成器
 * 自动生成完整的API文档
 */
export class APIDocGenerator {
  private config: DocConfig;
  private tools: Tool[];
  private plugins: Plugin[];

  constructor(
    tools: Tool[] = TOOLS,
    plugins: Plugin[] = [],
    config: Partial<DocConfig> = {}
  ) {
    this.config = { ...DEFAULT_DOC_CONFIG, ...config };
    this.tools = tools;
    this.plugins = plugins;
  }

  /**
   * 生成完整文档
   */
  generateFullDocs(): string {
    switch (this.config.outputFormat) {
      case 'markdown':
        return this.generateMarkdownDocs();
      case 'html':
        return this.generateHTMLDocs();
      case 'json':
        return this.generateJSONDocs();
      default:
        return this.generateMarkdownDocs();
    }
  }

  /**
   * 生成Markdown文档
   */
  private generateMarkdownDocs(): string {
    const sections = [
      this.generateHeader(),
      this.generateTableOfContents(),
      this.generateOverview(),
      this.generateToolsSection(),
      this.generatePluginsSection(),
      this.generateExamplesSection(),
      this.generateSchemasSection(),
      this.generateChangelog(),
    ];

    return sections.filter(Boolean).join('\n\n');
  }

  /**
   * 生成HTML文档
   */
  private generateHTMLDocs(): string {
    const markdown = this.generateMarkdownDocs();
    return this.convertMarkdownToHTML(markdown);
  }

  /**
   * 生成JSON文档
   */
  private generateJSONDocs(): string {
    const doc = {
      metadata: {
        title: this.config.title,
        version: this.config.version,
        description: this.config.description,
        author: this.config.author,
        generatedAt: new Date().toISOString(),
      },
      tools: this.tools.map(tool => this.generateToolDoc(tool)),
      plugins: this.plugins.map(plugin => ({
        name: plugin.name,
        version: plugin.version,
        description: plugin.description,
        author: plugin.author,
        tools: plugin.tools.map(tool => this.generateToolDoc(tool)),
      })),
    };

    return JSON.stringify(doc, null, 2);
  }

  /**
   * 生成文档头部
   */
  private generateHeader(): string {
    return `# ${this.config.title}

**Version:** ${this.config.version}  
**Author:** ${this.config.author}  
**Generated:** ${new Date().toISOString()}

${this.config.description}

---
`;
  }

  /**
   * 生成目录
   */
  private generateTableOfContents(): string {
    const sections = [
      'Overview',
      'Tools',
      'Plugins',
    ];

    if (this.config.includeExamples) {
      sections.push('Examples');
    }

    if (this.config.includeSchemas) {
      sections.push('Schemas');
    }

    if (this.config.includeChangelog) {
      sections.push('Changelog');
    }

    const toc = sections.map((section, index) => {
      const link = section.toLowerCase().replace(/\s+/g, '-');
      return `${index + 1}. [${section}](#${link})`;
    }).join('\n');

    return `## Table of Contents

${toc}

---
`;
  }

  /**
   * 生成概述
   */
  private generateOverview(): string {
    return `## Overview

This document provides comprehensive API documentation for the MySQL MCP Server, including all available tools, plugins, and usage examples.

### Features

- **Database Operations**: Complete set of MySQL database operations
- **Security**: Built-in security features and input validation
- **Caching**: Query result caching for improved performance
- **Monitoring**: Real-time connection pool monitoring
- **Plugins**: Extensible plugin system for custom tools
- **Documentation**: Auto-generated API documentation

### Quick Start

\`\`\`bash
# Install dependencies
npm install

# Start the server
npm start

# View available tools
curl http://localhost:3000/tools
\`\`\`

---
`;
  }

  /**
   * 生成工具部分
   */
  private generateToolsSection(): string {
    const categories = this.categorizeTools();
    let content = '## Tools\n\n';

    for (const [category, tools] of Object.entries(categories)) {
      content += `### ${category}\n\n`;
      
      for (const tool of tools) {
        content += this.generateToolMarkdown(tool);
      }
      
      content += '\n';
    }

    return content;
  }

  /**
   * 生成插件部分
   */
  private generatePluginsSection(): string {
    if (this.plugins.length === 0) {
      return '';
    }

    let content = '## Plugins\n\n';

    for (const plugin of this.plugins) {
      content += generatePluginDocs(plugin);
      content += '\n\n';
    }

    return content;
  }

  /**
   * 生成示例部分
   */
  private generateExamplesSection(): string {
    if (!this.config.includeExamples) {
      return '';
    }

    let content = '## Examples\n\n';

    // 基础查询示例
    content += `### Basic Query Examples

\`\`\`json
{
  "name": "mysql_query",
  "arguments": {
    "query": "SELECT * FROM users WHERE age > ?",
    "params": ["18"]
  }
}
\`\`\`

\`\`\`json
{
  "name": "mysql_show_tables",
  "arguments": {}
}
\`\`\`

### Data Manipulation Examples

\`\`\`json
{
  "name": "mysql_insert_data",
  "arguments": {
    "table_name": "users",
    "data": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "age": 25
    }
  }
}
\`\`\`

\`\`\`json
{
  "name": "mysql_update_data",
  "arguments": {
    "table_name": "users",
    "data": {
      "email": "<EMAIL>"
    },
    "where_clause": "id = 1"
  }
}
\`\`\`

### Schema Management Examples

\`\`\`json
{
  "name": "mysql_create_table",
  "arguments": {
    "table_name": "products",
    "columns": [
      {
        "name": "id",
        "type": "INT",
        "primary_key": true,
        "auto_increment": true
      },
      {
        "name": "name",
        "type": "VARCHAR(255)",
        "nullable": false
      },
      {
        "name": "price",
        "type": "DECIMAL(10,2)",
        "nullable": false
      }
    ]
  }
}
\`\`\`

---
`;

    return content;
  }

  /**
   * 生成模式部分
   */
  private generateSchemasSection(): string {
    if (!this.config.includeSchemas) {
      return '';
    }

    let content = '## Schemas\n\n';

    for (const tool of this.tools) {
      content += `### ${tool.name}\n\n`;
      content += `**Description:** ${tool.description}\n\n`;
      content += `**Input Schema:**\n\`\`\`json\n${JSON.stringify(tool.inputSchema, null, 2)}\n\`\`\`\n\n`;
    }

    return content;
  }

  /**
   * 生成变更日志
   */
  private generateChangelog(): string {
    if (!this.config.includeChangelog) {
      return '';
    }

    return `## Changelog

### Version ${this.config.version}

- Initial release
- Complete MySQL MCP server implementation
- Security features and input validation
- Caching system for improved performance
- Connection pool monitoring
- Plugin system for extensibility
- Auto-generated API documentation

---
`;
  }

  /**
   * 生成工具Markdown
   */
  private generateToolMarkdown(tool: Tool): string {
    const doc = this.generateToolDoc(tool);
    
    let content = `#### ${tool.name}\n\n`;
    content += `${tool.description}\n\n`;
    
    if (doc.examples.length > 0) {
      content += `**Examples:**\n\n`;
      for (const example of doc.examples) {
        content += `**${example.title}:** ${example.description}\n\n`;
        content += `\`\`\`json\n${JSON.stringify(example.input, null, 2)}\n\`\`\`\n\n`;
      }
    }
    
    content += `**Input Schema:**\n\`\`\`json\n${JSON.stringify(tool.inputSchema, null, 2)}\n\`\`\`\n\n`;
    
    return content;
  }

  /**
   * 生成工具文档
   */
  private generateToolDoc(tool: Tool): ToolDoc {
    const category = this.getToolCategory(tool.name);
    const tags = this.getToolTags(tool.name);
    const examples = this.getToolExamples(tool.name);

    return {
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
      examples,
      category,
      tags,
    };
  }

  /**
   * 分类工具
   */
  private categorizeTools(): Record<string, Tool[]> {
    const categories: Record<string, Tool[]> = {
      'Query Operations': [],
      'Data Manipulation': [],
      'Schema Management': [],
      'Information Retrieval': [],
      'Administration': [],
    };

    for (const tool of this.tools) {
      const category = this.getToolCategory(tool.name);
      if (categories[category]) {
        categories[category].push(tool);
      } else {
        categories['Administration'].push(tool);
      }
    }

    // 移除空分类
    for (const [category, tools] of Object.entries(categories)) {
      if (tools.length === 0) {
        delete categories[category];
      }
    }

    return categories;
  }

  /**
   * 获取工具分类
   */
  private getToolCategory(toolName: string): string {
    if (toolName.includes('query') || toolName.includes('select')) {
      return 'Query Operations';
    }
    if (toolName.includes('insert') || toolName.includes('update') || toolName.includes('delete')) {
      return 'Data Manipulation';
    }
    if (toolName.includes('create') || toolName.includes('drop') || toolName.includes('alter')) {
      return 'Schema Management';
    }
    if (toolName.includes('show') || toolName.includes('describe') || toolName.includes('get')) {
      return 'Information Retrieval';
    }
    return 'Administration';
  }

  /**
   * 获取工具标签
   */
  private getToolTags(toolName: string): string[] {
    const tags: string[] = [];
    
    if (toolName.includes('query')) tags.push('query');
    if (toolName.includes('select')) tags.push('read');
    if (toolName.includes('insert')) tags.push('write');
    if (toolName.includes('update')) tags.push('write');
    if (toolName.includes('delete')) tags.push('write');
    if (toolName.includes('create')) tags.push('schema');
    if (toolName.includes('drop')) tags.push('schema');
    if (toolName.includes('show')) tags.push('info');
    if (toolName.includes('describe')) tags.push('info');
    
    return tags;
  }

  /**
   * 获取工具示例
   */
  private getToolExamples(toolName: string): ToolExample[] {
    const examples: Record<string, ToolExample[]> = {
      'mysql_query': [
        {
          title: 'Simple SELECT',
          description: 'Query all users',
          input: {
            query: 'SELECT * FROM users',
            params: []
          },
          output: {
            success: true,
            data: [
              { id: 1, name: 'John Doe', email: '<EMAIL>' }
            ]
          }
        }
      ],
      'mysql_insert_data': [
        {
          title: 'Insert User',
          description: 'Insert a new user record',
          input: {
            table_name: 'users',
            data: {
              name: 'Jane Doe',
              email: '<EMAIL>',
              age: 30
            }
          },
          output: {
            success: true,
            affectedRows: 1,
            insertId: 2
          }
        }
      ],
      'mysql_create_table': [
        {
          title: 'Create Products Table',
          description: 'Create a products table with basic structure',
          input: {
            table_name: 'products',
            columns: [
              {
                name: 'id',
                type: 'INT',
                primary_key: true,
                auto_increment: true
              },
              {
                name: 'name',
                type: 'VARCHAR(255)',
                nullable: false
              },
              {
                name: 'price',
                type: 'DECIMAL(10,2)',
                nullable: false
              }
            ]
          },
          output: {
            success: true,
            message: 'Table created successfully'
          }
        }
      ]
    };

    return examples[toolName] || [];
  }

  /**
   * 转换Markdown为HTML
   */
  private convertMarkdownToHTML(markdown: string): string {
    // 简单的Markdown到HTML转换
    let html = markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/```json\n([\s\S]*?)\n```/g, '<pre><code class="language-json">$1</code></pre>')
      .replace(/```bash\n([\s\S]*?)\n```/g, '<pre><code class="language-bash">$1</code></pre>')
      .replace(/\n\n/g, '</p><p>');

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.config.title}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        code { font-family: 'Courier New', monospace; }
        h1, h2, h3, h4 { color: #333; }
        .toc { background: #f9f9f9; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
${html}
</body>
</html>`;
  }
}

// ==================== 工具函数 ====================

/**
 * 生成工具文档
 */
export function generateToolDocs(tools: Tool[]): string {
  const generator = new APIDocGenerator(tools);
  return generator.generateFullDocs();
}

/**
 * 生成插件文档
 */
export function generatePluginDocs(plugins: Plugin[]): string {
  const generator = new APIDocGenerator([], plugins);
  return generator.generateFullDocs();
}

/**
 * 生成完整文档
 */
export function generateCompleteDocs(
  tools: Tool[] = TOOLS,
  plugins: Plugin[] = [],
  config: Partial<DocConfig> = {}
): string {
  const generator = new APIDocGenerator(tools, plugins, config);
  return generator.generateFullDocs();
}

/**
 * 保存文档到文件
 */
export async function saveDocsToFile(
  content: string,
  filename: string,
  format: 'markdown' | 'html' | 'json' = 'markdown'
): Promise<void> {
  const fs = await import('fs/promises');
  const ext = format === 'markdown' ? 'md' : format;
  const fullFilename = `${filename}.${ext}`;
  
  await fs.writeFile(fullFilename, content, 'utf8');
  console.log(`Documentation saved to ${fullFilename}`);
} 