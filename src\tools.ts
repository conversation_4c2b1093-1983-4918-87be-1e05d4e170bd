/**
 * MySQL MCP服务器工具定义
 * 集中管理所有可用的MySQL操作工具
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';

/**
 * 所有可用的MySQL操作工具
 */
export const TOOLS: Tool[] = [
  {
    name: 'mysql_query',
    description: 'Execute a MySQL query (SELECT, SHOW, DESCRIBE, etc.)',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'The SQL query to execute',
        },
        params: {
          type: 'array',
          items: { type: 'string' },
          description: 'Optional parameters for prepared statements',
          default: [],
        },
      },
      required: ['query'],
    },
  },
  {
    name: 'mysql_show_tables',
    description: 'Show all tables in the current database',
    inputSchema: {
      type: 'object',
      properties: {},
      required: [],
    },
  },
  {
    name: 'mysql_describe_table',
    description: 'Describe the structure of a specific table',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to describe',
        },
      },
      required: ['table_name'],
    },
  },
  {
    name: 'mysql_select_data',
    description: 'Select data from a table with optional conditions',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to select from',
        },
        columns: {
          type: 'array',
          items: { type: 'string' },
          description: 'Columns to select (default: all columns)',
          default: ['*'],
        },
        where_clause: {
          type: 'string',
          description: 'Optional WHERE clause (without WHERE keyword)',
        },
        limit: {
          type: 'number',
          description: 'Optional LIMIT for results',
        },
      },
      required: ['table_name'],
    },
  },
  {
    name: 'mysql_insert_data',
    description: 'Insert new data into a table',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to insert into',
        },
        data: {
          type: 'object',
          description: 'Key-value pairs of column names and values to insert',
        },
      },
      required: ['table_name', 'data'],
    },
  },
  {
    name: 'mysql_update_data',
    description: 'Update existing data in a table',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to update',
        },
        data: {
          type: 'object',
          description: 'Key-value pairs of column names and new values',
        },
        where_clause: {
          type: 'string',
          description: 'WHERE clause to specify which records to update (without WHERE keyword)',
        },
      },
      required: ['table_name', 'data', 'where_clause'],
    },
  },
  {
    name: 'mysql_delete_data',
    description: 'Delete data from a table',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to delete from',
        },
        where_clause: {
          type: 'string',
          description: 'WHERE clause to specify which records to delete (without WHERE keyword)',
        },
      },
      required: ['table_name', 'where_clause'],
    },
  },
  {
    name: 'mysql_get_schema',
    description: 'Get database schema information including tables, columns, and constraints',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Optional specific table name to get schema for',
        },
      },
      required: [],
    },
  },
  {
    name: 'mysql_get_indexes',
    description: 'Get index information for a table or all tables',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Optional specific table name to get indexes for',
        },
      },
      required: [],
    },
  },
  {
    name: 'mysql_get_foreign_keys',
    description: 'Get foreign key constraints for a table or all tables',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Optional specific table name to get foreign keys for',
        },
      },
      required: [],
    },
  },
  {
    name: 'mysql_create_table',
    description: 'Create a new table with specified columns and constraints',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to create',
        },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { type: 'string' },
              nullable: { type: 'boolean', default: true },
              default: { type: 'string' },
              primary_key: { type: 'boolean', default: false },
              auto_increment: { type: 'boolean', default: false },
            },
            required: ['name', 'type'],
          },
          description: 'Array of column definitions',
        },
      },
      required: ['table_name', 'columns'],
    },
  },
  {
    name: 'mysql_drop_table',
    description: 'Drop (delete) a table from the database',
    inputSchema: {
      type: 'object',
      properties: {
        table_name: {
          type: 'string',
          description: 'Name of the table to drop',
        },
        if_exists: {
          type: 'boolean',
          description: 'Use IF EXISTS clause to avoid errors if table does not exist',
          default: true,
        },
      },
      required: ['table_name'],
    },
  },
];

/**
 * 工具名称枚举
 */
export enum ToolName {
  QUERY = 'mysql_query',
  SHOW_TABLES = 'mysql_show_tables',
  DESCRIBE_TABLE = 'mysql_describe_table',
  SELECT_DATA = 'mysql_select_data',
  INSERT_DATA = 'mysql_insert_data',
  UPDATE_DATA = 'mysql_update_data',
  DELETE_DATA = 'mysql_delete_data',
  GET_SCHEMA = 'mysql_get_schema',
  GET_INDEXES = 'mysql_get_indexes',
  GET_FOREIGN_KEYS = 'mysql_get_foreign_keys',
  CREATE_TABLE = 'mysql_create_table',
  DROP_TABLE = 'mysql_drop_table',
}

/**
 * 获取工具定义
 */
export function getTool(name: string): Tool | undefined {
  return TOOLS.find(tool => tool.name === name);
}

/**
 * 检查工具是否存在
 */
export function hasTool(name: string): boolean {
  return TOOLS.some(tool => tool.name === name);
} 