/**
 * MySQL MCP服务器插件系统
 * 支持自定义工具扩展和插件管理
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { EventEmitter } from 'events';

// ==================== 类型定义 ====================

/**
 * 插件接口
 */
export interface Plugin {
  name: string;                    // 插件名称
  version: string;                 // 插件版本
  description: string;             // 插件描述
  author: string;                  // 插件作者
  tools: Tool[];                   // 插件提供的工具
  dependencies?: string[];         // 依赖的其他插件
  config?: Record<string, any>;    // 插件配置
}

/**
 * 插件管理器接口
 */
export interface PluginManager {
  register(plugin: Plugin): void;
  unregister(pluginName: string): void;
  getPlugin(pluginName: string): Plugin | undefined;
  getAllPlugins(): Plugin[];
  getTools(): Tool[];
  validatePlugin(plugin: Plugin): boolean;
}

/**
 * 插件上下文接口
 */
export interface PluginContext {
  logger: any;                     // 日志记录器
  config: any;                     // 配置管理器
  cache: any;                      // 缓存管理器
  monitor: any;                    // 监控管理器
  database: any;                   // 数据库连接
}

/**
 * 插件生命周期接口
 */
export interface PluginLifecycle {
  onLoad?(context: PluginContext): Promise<void>;
  onUnload?(): Promise<void>;
  onEnable?(): Promise<void>;
  onDisable?(): Promise<void>;
}

/**
 * 插件元数据接口
 */
export interface PluginMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies: string[];
  enabled: boolean;
  loaded: boolean;
  loadTime: number;
  lastUsed: number;
}

// ==================== 插件基类 ====================

/**
 * 插件基类
 * 提供插件开发的基础功能
 */
export abstract class BasePlugin implements Plugin, PluginLifecycle {
  public name: string;
  public version: string;
  public description: string;
  public author: string;
  public tools: Tool[];
  public dependencies: string[];
  public config: Record<string, any>;
  public enabled: boolean = true;
  public loaded: boolean = false;
  public loadTime: number = 0;
  public lastUsed: number = 0;

  constructor(
    name: string,
    version: string,
    description: string,
    author: string,
    tools: Tool[] = [],
    dependencies: string[] = [],
    config: Record<string, any> = {}
  ) {
    this.name = name;
    this.version = version;
    this.description = description;
    this.author = author;
    this.tools = tools;
    this.dependencies = dependencies;
    this.config = config;
  }

  /**
   * 插件加载时调用
   */
  async onLoad(context: PluginContext): Promise<void> {
    this.loaded = true;
    this.loadTime = Date.now();
  }

  /**
   * 插件卸载时调用
   */
  async onUnload(): Promise<void> {
    this.loaded = false;
  }

  /**
   * 插件启用时调用
   */
  async onEnable(): Promise<void> {
    this.enabled = true;
  }

  /**
   * 插件禁用时调用
   */
  async onDisable(): Promise<void> {
    this.enabled = false;
  }

  /**
   * 更新最后使用时间
   */
  updateLastUsed(): void {
    this.lastUsed = Date.now();
  }

  /**
   * 获取插件元数据
   */
  getMetadata(): PluginMetadata {
    return {
      name: this.name,
      version: this.version,
      description: this.description,
      author: this.author,
      dependencies: this.dependencies,
      enabled: this.enabled,
      loaded: this.loaded,
      loadTime: this.loadTime,
      lastUsed: this.lastUsed,
    };
  }
}

// ==================== 插件管理器类 ====================

/**
 * 插件管理器
 * 负责插件的注册、管理和生命周期控制
 */
export class PluginManager extends EventEmitter {
  private plugins = new Map<string, Plugin>();
  private context: PluginContext | null = null;

  constructor() {
    super();
  }

  /**
   * 设置插件上下文
   */
  setContext(context: PluginContext): void {
    this.context = context;
  }

  /**
   * 注册插件
   */
  async register(plugin: Plugin): Promise<void> {
    // 验证插件
    if (!this.validatePlugin(plugin)) {
      throw new Error(`Invalid plugin: ${plugin.name}`);
    }

    // 检查依赖
    if (plugin.dependencies) {
      for (const dep of plugin.dependencies) {
        if (!this.plugins.has(dep)) {
          throw new Error(`Plugin ${plugin.name} depends on ${dep} which is not registered`);
        }
      }
    }

    // 注册插件
    this.plugins.set(plugin.name, plugin);

    // 如果是BasePlugin实例，调用onLoad
    if (this.context && plugin instanceof BasePlugin) {
      await plugin.onLoad(this.context);
    }

    this.emit('plugin:registered', plugin);
  }

  /**
   * 注销插件
   */
  async unregister(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new Error(`Plugin ${pluginName} not found`);
    }

    // 检查是否有其他插件依赖此插件
    for (const [name, p] of this.plugins.entries()) {
      if (p.dependencies && p.dependencies.includes(pluginName)) {
        throw new Error(`Cannot unregister ${pluginName}: ${name} depends on it`);
      }
    }

    // 如果是BasePlugin实例，调用onUnload
    if (plugin instanceof BasePlugin) {
      await plugin.onUnload();
    }

    this.plugins.delete(pluginName);
    this.emit('plugin:unregistered', plugin);
  }

  /**
   * 获取插件
   */
  getPlugin(pluginName: string): Plugin | undefined {
    return this.plugins.get(pluginName);
  }

  /**
   * 获取所有插件
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 获取所有工具
   */
  getTools(): Tool[] {
    const tools: Tool[] = [];
    for (const plugin of this.plugins.values()) {
      if (plugin.enabled !== false) {
        tools.push(...plugin.tools);
      }
    }
    return tools;
  }

  /**
   * 验证插件
   */
  validatePlugin(plugin: Plugin): boolean {
    // 检查必需字段
    if (!plugin.name || !plugin.version || !plugin.description || !plugin.author) {
      return false;
    }

    // 检查工具格式
    if (plugin.tools) {
      for (const tool of plugin.tools) {
        if (!tool.name || !tool.description || !tool.inputSchema) {
          return false;
        }
      }
    }

    // 检查工具名称唯一性
    const toolNames = new Set<string>();
    for (const plugin of this.plugins.values()) {
      for (const tool of plugin.tools) {
        if (toolNames.has(tool.name)) {
          return false;
        }
        toolNames.add(tool.name);
      }
    }

    return true;
  }

  /**
   * 启用插件
   */
  async enablePlugin(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new Error(`Plugin ${pluginName} not found`);
    }

    if (plugin instanceof BasePlugin) {
      await plugin.onEnable();
    }

    this.emit('plugin:enabled', plugin);
  }

  /**
   * 禁用插件
   */
  async disablePlugin(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new Error(`Plugin ${pluginName} not found`);
    }

    if (plugin instanceof BasePlugin) {
      await plugin.onDisable();
    }

    this.emit('plugin:disabled', plugin);
  }

  /**
   * 获取插件统计信息
   */
  getPluginStats(): {
    total: number;
    enabled: number;
    disabled: number;
    loaded: number;
    totalTools: number;
  } {
    let enabled = 0;
    let disabled = 0;
    let loaded = 0;
    let totalTools = 0;

    for (const plugin of this.plugins.values()) {
      if (plugin.enabled !== false) {
        enabled++;
      } else {
        disabled++;
      }

      if (plugin instanceof BasePlugin && plugin.loaded) {
        loaded++;
      }

      totalTools += plugin.tools.length;
    }

    return {
      total: this.plugins.size,
      enabled,
      disabled,
      loaded,
      totalTools,
    };
  }

  /**
   * 清理未使用的插件
   */
  async cleanupUnusedPlugins(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [name, plugin] of this.plugins.entries()) {
      if (plugin instanceof BasePlugin) {
        const age = now - plugin.lastUsed;
        if (age > maxAge && plugin.lastUsed > 0) {
          toRemove.push(name);
        }
      }
    }

    for (const name of toRemove) {
      try {
        await this.unregister(name);
      } catch (error) {
        // 忽略依赖错误
      }
    }
  }
}

// ==================== 内置插件示例 ====================

/**
 * 数据库统计插件
 * 提供数据库统计相关的工具
 */
export class DatabaseStatsPlugin extends BasePlugin {
  constructor() {
    super(
      'database-stats',
      '1.0.0',
      'Database statistics and monitoring tools',
      'MySQL MCP Team',
      [
        {
          name: 'mysql_get_table_stats',
          description: 'Get statistics for a specific table',
          inputSchema: {
            type: 'object',
            properties: {
              table_name: {
                type: 'string',
                description: 'Name of the table to get statistics for',
              },
            },
            required: ['table_name'],
          },
        },
        {
          name: 'mysql_get_database_size',
          description: 'Get the total size of the database',
          inputSchema: {
            type: 'object',
            properties: {},
            required: [],
          },
        },
        {
          name: 'mysql_get_slow_queries',
          description: 'Get slow query information',
          inputSchema: {
            type: 'object',
            properties: {
              limit: {
                type: 'number',
                description: 'Maximum number of queries to return',
                default: 10,
              },
            },
            required: [],
          },
        },
      ]
    );
  }

  async onLoad(context: PluginContext): Promise<void> {
    await super.onLoad(context);
    // 插件特定的初始化逻辑
  }
}

/**
 * 数据导出插件
 * 提供数据导出相关的工具
 */
export class DataExportPlugin extends BasePlugin {
  constructor() {
    super(
      'data-export',
      '1.0.0',
      'Data export and backup tools',
      'MySQL MCP Team',
      [
        {
          name: 'mysql_export_table',
          description: 'Export table data to JSON format',
          inputSchema: {
            type: 'object',
            properties: {
              table_name: {
                type: 'string',
                description: 'Name of the table to export',
              },
              format: {
                type: 'string',
                description: 'Export format (json, csv)',
                enum: ['json', 'csv'],
                default: 'json',
              },
              limit: {
                type: 'number',
                description: 'Maximum number of rows to export',
                default: 1000,
              },
            },
            required: ['table_name'],
          },
        },
        {
          name: 'mysql_backup_table',
          description: 'Create a backup of a table structure and data',
          inputSchema: {
            type: 'object',
            properties: {
              table_name: {
                type: 'string',
                description: 'Name of the table to backup',
              },
              include_data: {
                type: 'boolean',
                description: 'Whether to include data in backup',
                default: true,
              },
            },
            required: ['table_name'],
          },
        },
      ]
    );
  }
}

// ==================== 工具函数 ====================

/**
 * 创建插件实例
 */
export function createPlugin(
  name: string,
  version: string,
  description: string,
  author: string,
  tools: Tool[],
  dependencies: string[] = [],
  config: Record<string, any> = {}
): Plugin {
  return {
    name,
    version,
    description,
    author,
    tools,
    dependencies,
    config,
  };
}

/**
 * 验证工具格式
 */
export function validateTool(tool: Tool): boolean {
  return !!(tool.name && tool.description && tool.inputSchema);
}

/**
 * 生成插件文档
 */
export function generatePluginDocs(plugin: Plugin): string {
  return `# ${plugin.name}

**Version:** ${plugin.version}
**Author:** ${plugin.author}
**Description:** ${plugin.description}

## Tools

${plugin.tools.map(tool => `### ${tool.name}

${tool.description}

**Input Schema:**
\`\`\`json
${JSON.stringify(tool.inputSchema, null, 2)}
\`\`\`

`).join('\n')}
`;
} 