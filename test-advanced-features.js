#!/usr/bin/env node

/**
 * MySQL MCP 服务器高级功能测试脚本
 * 验证缓存、监控、插件和文档生成功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 MySQL MCP 服务器高级功能测试');
console.log('====================================');

// 检查新增的模块文件
const srcDir = path.join(__dirname, 'src');
const files = fs.readdirSync(srcDir);

console.log('\n📁 新增模块检查:');
console.log('------------------');

const newModules = ['cache.ts', 'monitor.ts', 'plugins.ts', 'docs.ts'];
const foundNewModules = files.filter(file => newModules.includes(file));

console.log('✅ 缓存模块:', foundNewModules.includes('cache.ts') ? '存在' : '缺失');
console.log('✅ 监控模块:', foundNewModules.includes('monitor.ts') ? '存在' : '缺失');
console.log('✅ 插件模块:', foundNewModules.includes('plugins.ts') ? '存在' : '缺失');
console.log('✅ 文档模块:', foundNewModules.includes('docs.ts') ? '存在' : '缺失');

// 检查代码行数
console.log('\n📊 代码统计:');
console.log('------------------');

let totalLines = 0;
const allFiles = files.filter(file => file.endsWith('.ts'));
allFiles.forEach(file => {
  const filePath = path.join(srcDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n').length;
  totalLines += lines;
  console.log(`📄 ${file}: ${lines} 行`);
});

console.log(`📈 总行数: ${totalLines} 行`);

// 检查缓存功能
console.log('\n💾 缓存功能检查:');
console.log('------------------');

const cacheContent = fs.readFileSync(path.join(srcDir, 'cache.ts'), 'utf8');
const cacheFeatures = [
  'CacheManager',
  'CacheEntry',
  'CacheStats',
  'getStats',
  'set',
  'get',
  'clear'
];

cacheFeatures.forEach(feature => {
  const hasFeature = cacheContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查监控功能
console.log('\n📊 监控功能检查:');
console.log('------------------');

const monitorContent = fs.readFileSync(path.join(srcDir, 'monitor.ts'), 'utf8');
const monitorFeatures = [
  'PoolMonitor',
  'PerformanceMonitor',
  'PoolStatus',
  'PerformanceMetrics',
  'recordQuery',
  'getMetrics',
  'getPoolStatus'
];

monitorFeatures.forEach(feature => {
  const hasFeature = monitorContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查插件功能
console.log('\n🔌 插件功能检查:');
console.log('------------------');

const pluginsContent = fs.readFileSync(path.join(srcDir, 'plugins.ts'), 'utf8');
const pluginFeatures = [
  'PluginManager',
  'BasePlugin',
  'Plugin',
  'register',
  'getTools',
  'validatePlugin',
  'DatabaseStatsPlugin',
  'DataExportPlugin'
];

pluginFeatures.forEach(feature => {
  const hasFeature = pluginsContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查文档功能
console.log('\n📚 文档功能检查:');
console.log('------------------');

const docsContent = fs.readFileSync(path.join(srcDir, 'docs.ts'), 'utf8');
const docsFeatures = [
  'APIDocGenerator',
  'generateCompleteDocs',
  'generateMarkdownDocs',
  'generateHTMLDocs',
  'generateJSONDocs',
  'categorizeTools'
];

docsFeatures.forEach(feature => {
  const hasFeature = docsContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查主文件集成
console.log('\n🔗 主文件集成检查:');
console.log('------------------');

const indexContent = fs.readFileSync(path.join(srcDir, 'index.ts'), 'utf8');
const integrationFeatures = [
  'CacheManager',
  'PoolMonitor',
  'PerformanceMonitor',
  'PluginManager',
  'generateCompleteDocs',
  'mysql_get_cache_stats',
  'mysql_get_pool_stats',
  'mysql_clear_cache',
  'mysql_generate_docs'
];

integrationFeatures.forEach(feature => {
  const hasFeature = indexContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已集成' : '未集成'}`);
});

// 功能统计
console.log('\n📋 功能统计:');
console.log('================');

const cacheImplemented = cacheFeatures.filter(f => cacheContent.includes(f)).length;
const monitorImplemented = monitorFeatures.filter(f => monitorContent.includes(f)).length;
const pluginImplemented = pluginFeatures.filter(f => pluginsContent.includes(f)).length;
const docsImplemented = docsFeatures.filter(f => docsContent.includes(f)).length;
const integrationImplemented = integrationFeatures.filter(f => indexContent.includes(f)).length;

console.log(`💾 缓存功能: ${cacheImplemented}/${cacheFeatures.length} (${(cacheImplemented/cacheFeatures.length*100).toFixed(1)}%)`);
console.log(`📊 监控功能: ${monitorImplemented}/${monitorFeatures.length} (${(monitorImplemented/monitorFeatures.length*100).toFixed(1)}%)`);
console.log(`🔌 插件功能: ${pluginImplemented}/${pluginFeatures.length} (${(pluginImplemented/pluginFeatures.length*100).toFixed(1)}%)`);
console.log(`📚 文档功能: ${docsImplemented}/${docsFeatures.length} (${(docsImplemented/docsFeatures.length*100).toFixed(1)}%)`);
console.log(`🔗 集成功能: ${integrationImplemented}/${integrationFeatures.length} (${(integrationImplemented/integrationFeatures.length*100).toFixed(1)}%)`);

// 总体评估
console.log('\n🎯 总体评估:');
console.log('================');

const totalFeatures = cacheFeatures.length + monitorFeatures.length + pluginFeatures.length + docsFeatures.length + integrationFeatures.length;
const totalImplemented = cacheImplemented + monitorImplemented + pluginImplemented + docsImplemented + integrationImplemented;
const overallPercentage = (totalImplemented / totalFeatures * 100).toFixed(1);

console.log(`📈 总体实现率: ${overallPercentage}%`);
console.log(`📦 新增模块: ${foundNewModules.length}/${newModules.length}`);
console.log(`📄 代码增长: +${totalLines - 1302} 行 (从1302行增加到${totalLines}行)`);

if (parseFloat(overallPercentage) >= 80) {
  console.log('🎉 高级功能实现优秀！');
} else if (parseFloat(overallPercentage) >= 60) {
  console.log('✅ 高级功能实现良好！');
} else {
  console.log('⚠️ 高级功能需要进一步完善！');
}

console.log('\n🚀 新增功能总结:');
console.log('==================');
console.log('💾 缓存机制: 查询结果缓存，提升性能');
console.log('📊 连接池监控: 实时监控连接池状态');
console.log('📈 性能指标: 详细的性能统计和监控');
console.log('🔌 插件系统: 支持自定义工具扩展');
console.log('📚 API文档: 自动生成API文档');

console.log('\n🎉 高级功能测试完成！');
console.log('===================================='); 