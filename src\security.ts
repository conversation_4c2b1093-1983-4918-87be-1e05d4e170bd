/**
 * MySQL MCP服务器安全工具类
 * 提供输入验证、SQL注入防护、频率限制等安全功能
 */

import type { SecurityConfig } from './config.js';

// ==================== 类型定义 ====================

/**
 * 频率限制记录接口
 */
export interface RateLimitEntry {
  count: number;     // 当前请求数
  resetTime: number; // 重置时间
}

/**
 * 查询结果接口
 */
export interface QueryResult {
  success: boolean;
  data?: any;
  error?: string;
  affectedRows?: number;
  insertId?: number;
}

// ==================== 常量定义 ====================

/**
 * 危险操作模式，用于SQL注入防护
 */
export const DANGEROUS_PATTERNS = [
  /\b(LOAD_FILE|INTO OUTFILE|INTO DUMPFILE)\b/i, // 文件操作
  /\b(SYSTEM|EXEC|SHELL)\b/i,                    // 系统命令执行
  /\bINTO\s+OUTFILE\b/i,                         // 文件输出
  /\bLOAD\s+DATA\b/i,                            // 数据加载
  /\bUNION\s+SELECT\b/i,                         // UNION注入
  /\bOR\s+1\s*=\s*1\b/i,                         // 布尔注入
  /\bOR\s+'1'\s*=\s*'1'\b/i,                     // 字符串注入
  /\bDROP\s+DATABASE\b/i,                         // 删除数据库
  /\bTRUNCATE\s+TABLE\b/i,                        // 清空表
  /\bDELETE\s+FROM\s+.*\s+WHERE\s+1\s*=\s*1\b/i, // 危险删除
] as const;

// ==================== 安全验证器类 ====================

/**
 * 安全验证器类
 * 提供输入验证、SQL注入防护等功能
 */
export class SecurityValidator {
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * 验证用户输入的安全性
   */
  validateInput(input: any, fieldName: string): void {
    if (typeof input === 'string') {
      // 检查空字节注入攻击
      if (input.includes('\0')) {
        throw new Error(`Invalid character in ${fieldName}`);
      }
      // 检查输入长度
      if (input.length > this.config.maxInputLength) {
        throw new Error(`${fieldName} exceeds maximum length of ${this.config.maxInputLength}`);
      }
      // 检查SQL注入模式
      this.checkSQLInjection(input, fieldName);
    }
  }

  /**
   * 验证SQL查询的安全性
   */
  validateQuery(query: string): void {
    // 检查查询长度
    if (query.length > this.config.maxQueryLength) {
      throw new Error(`Query exceeds maximum allowed length of ${this.config.maxQueryLength}`);
    }

    // 检查危险操作模式
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(query)) {
        throw new Error('Query contains prohibited operations');
      }
    }

    // 验证查询类型
    const queryType = query.trim().split(/\s+/)[0]?.toUpperCase();
    if (queryType && !this.config.allowedQueryTypes.includes(queryType)) {
      throw new Error(`Query type '${queryType}' is not allowed`);
    }

    // 检查SQL注入
    this.checkSQLInjection(query, 'query');
  }

  /**
   * 验证表名的合法性
   */
  validateTableName(tableName: string): void {
    // 只允许字母、数字、下划线和短横线
    if (!/^[a-zA-Z0-9_-]+$/.test(tableName)) {
      throw new Error('Invalid table name format');
    }
    
    // 检查表名长度
    if (tableName.length > 64) {
      throw new Error('Table name exceeds maximum length of 64 characters');
    }

    // 检查SQL注入
    this.checkSQLInjection(tableName, 'table_name');
  }

  /**
   * 检查SQL注入攻击
   */
  private checkSQLInjection(input: string, fieldName: string): void {
    const sqlInjectionPatterns = [
      /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i,           // OR/AND 数字比较
      /(\bOR\b|\bAND\b)\s+['"]\w+['"]\s*=\s*['"]\w+['"]/i, // OR/AND 字符串比较
      /\bUNION\s+SELECT\b/i,                           // UNION SELECT
      /\bEXEC\b/i,                                     // EXEC
      /\bEXECUTE\b/i,                                  // EXECUTE
      /\bWAITFOR\b/i,                                  // WAITFOR
      /\bDELAY\b/i,                                    // DELAY
      /;\s*$/i,                                        // 分号结尾
      /--\s*$/i,                                       // SQL注释
      /\/\*.*\*\//i,                                   // 多行注释
    ];

    for (const pattern of sqlInjectionPatterns) {
      if (pattern.test(input)) {
        throw new Error(`Potential SQL injection detected in ${fieldName}`);
      }
    }
  }
}

// ==================== 频率限制器类 ====================

/**
 * 频率限制管理器
 */
export class RateLimiter {
  private rateLimitMap = new Map<string, RateLimitEntry>();
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * 检查请求频率限制
   */
  checkRateLimit(identifier: string = 'default'): void {
    const now = Date.now();
    const entry = this.rateLimitMap.get(identifier);
    
    // 如果没有记录或时间窗口已过期，重置计数器
    if (!entry || now > entry.resetTime) {
      this.rateLimitMap.set(identifier, {
        count: 1,
        resetTime: now + this.config.rateLimitWindow
      });
      return;
    }
    
    // 检查是否超过频率限制
    if (entry.count >= this.config.rateLimitMax) {
      throw new Error(`Rate limit exceeded. Maximum ${this.config.rateLimitMax} requests per ${this.config.rateLimitWindow / 1000} seconds.`);
    }
    
    // 增加请求计数
    entry.count++;
  }

  /**
   * 清理过期的频率限制记录
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.rateLimitMap.entries()) {
      if (now > entry.resetTime) {
        this.rateLimitMap.delete(key);
      }
    }
  }

  /**
   * 获取当前频率限制状态
   */
  getRateLimitStatus(identifier: string = 'default'): { count: number; remaining: number; resetTime: number } | null {
    const entry = this.rateLimitMap.get(identifier);
    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (now > entry.resetTime) {
      return null;
    }

    return {
      count: entry.count,
      remaining: Math.max(0, this.config.rateLimitMax - entry.count),
      resetTime: entry.resetTime,
    };
  }
}

// ==================== 日志记录器类 ====================

/**
 * 日志记录工具类
 */
export class Logger {
  private sessionId: string;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }

  /**
   * 记录信息日志
   */
  info(message: string, data?: any): void {
    this.log('INFO', message, data);
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: any): void {
    this.log('ERROR', message, error);
  }

  /**
   * 记录安全事件
   */
  security(event: string, details?: any): void {
    this.log('SECURITY', event, details);
  }

  /**
   * 记录调试信息
   */
  debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      this.log('DEBUG', message, data);
    }
  }

  /**
   * 记录警告信息
   */
  warn(message: string, data?: any): void {
    this.log('WARN', message, data);
  }

  private log(level: string, message: string, data?: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      sessionId: this.sessionId,
      message,
      data: data ? (typeof data === 'object' ? JSON.stringify(data) : data) : undefined,
    };

    const output = level === 'ERROR' || level === 'SECURITY' ? console.error : console.log;
    output(`[${level}]`, JSON.stringify(logEntry));
  }
}

// ==================== 工具函数 ====================

/**
 * 清理错误信息中的敏感信息
 */
export function sanitizeError(error: any): string {
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  return errorMessage
    .replace(/password[^\s]*/gi, 'password=***')
    .replace(/host[^\s]*/gi, 'host=***')
    .replace(/user[^\s]*/gi, 'user=***')
    .replace(/localhost[^\s]*/gi, 'host=***')
    .replace(/127\.0\.0\.1[^\s]*/gi, 'host=***');
}

/**
 * 验证IP地址格式
 */
export function isValidIP(ip: string): boolean {
  const ipv4Pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Pattern = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  
  return ipv4Pattern.test(ip) || ipv6Pattern.test(ip);
}

/**
 * 生成安全的随机字符串
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
} 