#!/usr/bin/env node

/**
 * MySQL MCP 服务器优化验证脚本
 * 用于验证优化后的代码结构和功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 MySQL MCP 服务器优化验证');
console.log('================================');

// 检查文件结构
const srcDir = path.join(__dirname, 'src');
const files = fs.readdirSync(srcDir);

console.log('\n📁 文件结构检查:');
console.log('------------------');

const expectedFiles = ['index.ts', 'config.ts', 'tools.ts', 'security.ts'];
const foundFiles = files.filter(file => file.endsWith('.ts'));

console.log('✅ 主文件:', foundFiles.includes('index.ts') ? '存在' : '缺失');
console.log('✅ 配置文件:', foundFiles.includes('config.ts') ? '存在' : '缺失');
console.log('✅ 工具文件:', foundFiles.includes('tools.ts') ? '存在' : '缺失');
console.log('✅ 安全文件:', foundFiles.includes('security.ts') ? '存在' : '缺失');

// 检查代码行数
console.log('\n📊 代码统计:');
console.log('------------------');

let totalLines = 0;
foundFiles.forEach(file => {
  const filePath = path.join(srcDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n').length;
  totalLines += lines;
  console.log(`📄 ${file}: ${lines} 行`);
});

console.log(`📈 总行数: ${totalLines} 行`);

// 检查模块化程度
console.log('\n🔧 模块化检查:');
console.log('------------------');

const indexContent = fs.readFileSync(path.join(srcDir, 'index.ts'), 'utf8');
const configContent = fs.readFileSync(path.join(srcDir, 'config.ts'), 'utf8');
const toolsContent = fs.readFileSync(path.join(srcDir, 'tools.ts'), 'utf8');
const securityContent = fs.readFileSync(path.join(srcDir, 'security.ts'), 'utf8');

// 检查导入语句
const importStatements = indexContent.match(/import.*from.*['"]\.\/.*['"]/g);
console.log('✅ 模块导入:', importStatements ? `${importStatements.length} 个模块导入` : '无模块导入');

// 检查类型定义
const typeDefinitions = [
  indexContent.match(/interface.*{/g),
  configContent.match(/interface.*{/g),
  toolsContent.match(/interface.*{/g),
  securityContent.match(/interface.*{/g)
].filter(Boolean).flat();

console.log('✅ 类型定义:', `${typeDefinitions.length} 个接口定义`);

// 检查安全特性
const securityFeatures = [
  'SecurityValidator',
  'RateLimiter',
  'Logger',
  'sanitizeError',
  'DANGEROUS_PATTERNS'
];

console.log('\n🛡️ 安全特性检查:');
console.log('------------------');

securityFeatures.forEach(feature => {
  const hasFeature = securityContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查工具定义
const toolFeatures = [
  'TOOLS',
  'ToolName',
  'getTool',
  'hasTool'
];

console.log('\n🛠️ 工具定义检查:');
console.log('------------------');

toolFeatures.forEach(feature => {
  const hasFeature = toolsContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 检查配置管理
const configFeatures = [
  'ConfigLoader',
  'loadConfig',
  'validateConfig',
  'DEFAULT_CONFIG'
];

console.log('\n⚙️ 配置管理检查:');
console.log('------------------');

configFeatures.forEach(feature => {
  const hasFeature = configContent.includes(feature);
  console.log(`${hasFeature ? '✅' : '❌'} ${feature}: ${hasFeature ? '已实现' : '未实现'}`);
});

// 优化总结
console.log('\n📋 优化总结:');
console.log('================');

const originalLines = 1064; // 原始代码行数
const optimizedLines = totalLines;
const reduction = ((originalLines - optimizedLines) / originalLines * 100).toFixed(1);

console.log(`📉 代码行数减少: ${reduction}% (${originalLines} → ${optimizedLines})`);
console.log(`📦 模块化程度: ${foundFiles.length} 个模块文件`);
console.log(`🔒 安全特性: ${securityFeatures.filter(f => securityContent.includes(f)).length}/${securityFeatures.length}`);
console.log(`🛠️ 工具特性: ${toolFeatures.filter(f => toolsContent.includes(f)).length}/${toolFeatures.length}`);
console.log(`⚙️ 配置特性: ${configFeatures.filter(f => configContent.includes(f)).length}/${configFeatures.length}`);

// 检查package.json
console.log('\n📦 依赖检查:');
console.log('------------------');

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const hasTypeScript = packageJson.devDependencies && packageJson.devDependencies.typescript;
  const hasMCP = packageJson.dependencies && packageJson.dependencies['@modelcontextprotocol/sdk'];
  const hasMySQL = packageJson.dependencies && packageJson.dependencies.mysql2;
  
  console.log(`✅ TypeScript: ${hasTypeScript ? '已安装' : '未安装'}`);
  console.log(`✅ MCP SDK: ${hasMCP ? '已安装' : '未安装'}`);
  console.log(`✅ MySQL2: ${hasMySQL ? '已安装' : '未安装'}`);
} catch (error) {
  console.log('❌ 无法读取 package.json');
}

console.log('\n🎉 优化验证完成！');
console.log('================================'); 