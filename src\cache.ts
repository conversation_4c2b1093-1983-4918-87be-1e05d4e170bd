/**
 * MySQL MCP服务器缓存管理模块
 * 提供查询结果缓存、缓存统计和缓存管理功能
 */

import crypto from 'crypto';

// ==================== 类型定义 ====================

/**
 * 缓存条目接口
 */
export interface CacheEntry {
  data: any;           // 缓存的数据
  timestamp: number;    // 创建时间戳
  ttl: number;         // 生存时间（毫秒）
  hits: number;        // 命中次数
  size: number;        // 数据大小（字节）
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalEntries: number;     // 总条目数
  totalHits: number;        // 总命中次数
  totalMisses: number;      // 总未命中次数
  totalSize: number;        // 总大小（字节）
  hitRate: number;          // 命中率
  memoryUsage: number;      // 内存使用量
  averageResponseTime: number; // 平均响应时间
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  maxSize: number;          // 最大缓存大小（字节）
  maxEntries: number;       // 最大条目数
  defaultTTL: number;       // 默认生存时间（毫秒）
  cleanupInterval: number;  // 清理间隔（毫秒）
  enableCompression: boolean; // 是否启用压缩
}

// ==================== 默认配置 ====================

/**
 * 默认缓存配置
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxSize: 100 * 1024 * 1024,  // 100MB
  maxEntries: 1000,             // 1000个条目
  defaultTTL: 5 * 60 * 1000,   // 5分钟
  cleanupInterval: 60 * 1000,   // 1分钟清理一次
  enableCompression: false,     // 默认不启用压缩
};

// ==================== 缓存管理器类 ====================

/**
 * 缓存管理器
 * 提供查询结果缓存、统计和管理功能
 */
export class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private performanceMetrics: {
    responseTimes: number[];
    lastCleanup: number;
  };

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config };
    this.stats = this.initializeStats();
    this.performanceMetrics = {
      responseTimes: [],
      lastCleanup: Date.now(),
    };
    this.startCleanupTimer();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): CacheStats {
    return {
      totalEntries: 0,
      totalHits: 0,
      totalMisses: 0,
      totalSize: 0,
      hitRate: 0,
      memoryUsage: 0,
      averageResponseTime: 0,
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(query: string, params: any[] = []): string {
    const keyData = JSON.stringify({ query, params });
    return crypto.createHash('md5').update(keyData).digest('hex');
  }

  /**
   * 计算数据大小
   */
  private calculateSize(data: any): number {
    try {
      return Buffer.byteLength(JSON.stringify(data), 'utf8');
    } catch {
      return 0;
    }
  }

  /**
   * 检查缓存是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.timestamp + entry.ttl;
  }

  /**
   * 清理过期条目
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleanedSize = 0;
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        cleanedSize += entry.size;
        cleanedCount++;
        this.cache.delete(key);
      }
    }

    if (cleanedCount > 0) {
      this.stats.totalSize -= cleanedSize;
      this.stats.totalEntries -= cleanedCount;
      this.performanceMetrics.lastCleanup = now;
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * 获取缓存数据
   */
  get(query: string, params: any[] = []): any | null {
    const startTime = Date.now();
    const key = this.generateCacheKey(query, params);
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.totalMisses++;
      this.updatePerformanceMetrics(Date.now() - startTime);
      return null;
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.totalSize -= entry.size;
      this.stats.totalEntries--;
      this.stats.totalMisses++;
      this.updatePerformanceMetrics(Date.now() - startTime);
      return null;
    }

    // 更新命中统计
    entry.hits++;
    this.stats.totalHits++;
    this.updatePerformanceMetrics(Date.now() - startTime);
    this.updateStats();

    return entry.data;
  }

  /**
   * 设置缓存数据
   */
  set(query: string, params: any[] = [], data: any, ttl?: number): boolean {
    const key = this.generateCacheKey(query, params);
    const size = this.calculateSize(data);
    const entryTTL = ttl || this.config.defaultTTL;

    // 检查缓存大小限制
    if (size > this.config.maxSize) {
      return false; // 数据太大，不缓存
    }

    // 如果缓存已满，清理最旧的条目
    while (this.stats.totalSize + size > this.config.maxSize || 
           this.stats.totalEntries >= this.config.maxEntries) {
      this.evictOldestEntry();
    }

    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      ttl: entryTTL,
      hits: 0,
      size,
    };

    // 如果已存在，先移除旧条目
    const existingEntry = this.cache.get(key);
    if (existingEntry) {
      this.stats.totalSize -= existingEntry.size;
      this.stats.totalEntries--;
    }

    this.cache.set(key, entry);
    this.stats.totalSize += size;
    this.stats.totalEntries++;
    this.updateStats();

    return true;
  }

  /**
   * 移除最旧的条目
   */
  private evictOldestEntry(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.cache.get(oldestKey)!;
      this.cache.delete(oldestKey);
      this.stats.totalSize -= entry.size;
      this.stats.totalEntries--;
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(responseTime: number): void {
    this.performanceMetrics.responseTimes.push(responseTime);
    
    // 只保留最近100个响应时间
    if (this.performanceMetrics.responseTimes.length > 100) {
      this.performanceMetrics.responseTimes.shift();
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const totalRequests = this.stats.totalHits + this.stats.totalMisses;
    this.stats.hitRate = totalRequests > 0 ? this.stats.totalHits / totalRequests : 0;
    
    if (this.performanceMetrics.responseTimes.length > 0) {
      const sum = this.performanceMetrics.responseTimes.reduce((a, b) => a + b, 0);
      this.stats.averageResponseTime = sum / this.performanceMetrics.responseTimes.length;
    }

    // 估算内存使用量
    this.stats.memoryUsage = this.stats.totalSize + (this.cache.size * 100); // 每个条目约100字节开销
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.stats = this.initializeStats();
    this.performanceMetrics.responseTimes = [];
  }

  /**
   * 获取缓存大小
   */
  getSize(): number {
    return this.cache.size;
  }

  /**
   * 检查缓存是否为空
   */
  isEmpty(): boolean {
    return this.cache.size === 0;
  }

  /**
   * 获取缓存键列表
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 检查键是否存在
   */
  hasKey(query: string, params: any[] = []): boolean {
    const key = this.generateCacheKey(query, params);
    const entry = this.cache.get(key);
    return entry !== undefined && !this.isExpired(entry);
  }

  /**
   * 移除特定缓存条目
   */
  remove(query: string, params: any[] = []): boolean {
    const key = this.generateCacheKey(query, params);
    const entry = this.cache.get(key);
    
    if (entry) {
      this.cache.delete(key);
      this.stats.totalSize -= entry.size;
      this.stats.totalEntries--;
      this.updateStats();
      return true;
    }
    
    return false;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 如果清理间隔改变，重启定时器
    if (newConfig.cleanupInterval) {
      this.stopCleanupTimer();
      this.startCleanupTimer();
    }
  }

  /**
   * 获取配置
   */
  getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

// ==================== 工具函数 ====================

/**
 * 格式化缓存大小
 */
export function formatCacheSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number): string {
  return `${(value * 100).toFixed(2)}%`;
}

/**
 * 格式化时间
 */
export function formatTime(ms: number): string {
  if (ms < 1000) return `${ms.toFixed(2)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
} 