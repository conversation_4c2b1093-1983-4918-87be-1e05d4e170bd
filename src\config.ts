/**
 * MySQL MCP服务器配置文件
 * 集中管理所有配置项，支持环境变量覆盖
 */

// ==================== 类型定义 ====================

/**
 * MySQL数据库连接配置接口
 */
export interface MySQLConfig {
  host: string;              // 数据库主机地址
  port: number;              // 数据库端口
  user: string;              // 数据库用户名
  password: string;          // 数据库密码
  database: string;          // 数据库名称
  connectionLimit?: number;  // 连接池最大连接数
  connectTimeout?: number;   // 连接超时时间
  idleTimeout?: number;      // 空闲连接超时时间
  ssl?: boolean;            // 是否启用SSL
}

/**
 * 安全配置接口，用于防止滥用和攻击
 */
export interface SecurityConfig {
  maxQueryLength: number;       // 最大查询长度
  allowedQueryTypes: string[];  // 允许的查询类型白名单
  maxResultRows: number;        // 最大返回行数
  queryTimeout: number;         // 查询超时时间
  rateLimitWindow: number;      // 频率限制窗口时间
  rateLimitMax: number;         // 频率限制最大请求数
  maxInputLength: number;       // 最大输入长度
}

/**
 * 服务器配置接口
 */
export interface ServerConfig {
  mysql: MySQLConfig;
  security: SecurityConfig;
}

// ==================== 默认配置 ====================

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
  MYSQL: {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: '',
    connectionLimit: 10,
    connectTimeout: 60000,
    idleTimeout: 60000,
    ssl: false,
  },
  SECURITY: {
    maxQueryLength: 10000,
    allowedQueryTypes: ['SELECT', 'SHOW', 'DESCRIBE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER'],
    maxResultRows: 1000,
    queryTimeout: 30000,
    rateLimitWindow: 60000,
    rateLimitMax: 100,
    maxInputLength: 1000,
  },
} as const;

// ==================== 配置加载器 ====================

/**
 * 配置加载器类
 * 负责从环境变量加载配置并应用默认值
 */
export class ConfigLoader {
  /**
   * 加载MySQL配置
   */
  static loadMySQLConfig(): MySQLConfig {
    return {
      host: process.env.MYSQL_HOST || DEFAULT_CONFIG.MYSQL.host,
      port: parseInt(process.env.MYSQL_PORT || String(DEFAULT_CONFIG.MYSQL.port)),
      user: process.env.MYSQL_USER || DEFAULT_CONFIG.MYSQL.user,
      password: process.env.MYSQL_PASSWORD || DEFAULT_CONFIG.MYSQL.password,
      database: process.env.MYSQL_DATABASE || DEFAULT_CONFIG.MYSQL.database,
      connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || String(DEFAULT_CONFIG.MYSQL.connectionLimit)),
      connectTimeout: parseInt(process.env.MYSQL_CONNECT_TIMEOUT || String(DEFAULT_CONFIG.MYSQL.connectTimeout)),
      idleTimeout: parseInt(process.env.MYSQL_IDLE_TIMEOUT || String(DEFAULT_CONFIG.MYSQL.idleTimeout)),
      ssl: process.env.MYSQL_SSL === 'true',
    };
  }

  /**
   * 加载安全配置
   */
  static loadSecurityConfig(): SecurityConfig {
    return {
      maxQueryLength: parseInt(process.env.MAX_QUERY_LENGTH || String(DEFAULT_CONFIG.SECURITY.maxQueryLength)),
      allowedQueryTypes: (process.env.ALLOWED_QUERY_TYPES || DEFAULT_CONFIG.SECURITY.allowedQueryTypes.join(',')).split(','),
      maxResultRows: parseInt(process.env.MAX_RESULT_ROWS || String(DEFAULT_CONFIG.SECURITY.maxResultRows)),
      queryTimeout: parseInt(process.env.QUERY_TIMEOUT || String(DEFAULT_CONFIG.SECURITY.queryTimeout)),
      rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || String(DEFAULT_CONFIG.SECURITY.rateLimitWindow)),
      rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || String(DEFAULT_CONFIG.SECURITY.rateLimitMax)),
      maxInputLength: parseInt(process.env.MAX_INPUT_LENGTH || String(DEFAULT_CONFIG.SECURITY.maxInputLength)),
    };
  }

  /**
   * 加载完整配置
   */
  static loadConfig(): ServerConfig {
    return {
      mysql: this.loadMySQLConfig(),
      security: this.loadSecurityConfig(),
    };
  }

  /**
   * 验证配置的有效性
   */
  static validateConfig(config: ServerConfig): void {
    // 验证MySQL配置
    if (!config.mysql.host) {
      throw new Error('MySQL host is required');
    }
    if (config.mysql.port < 1 || config.mysql.port > 65535) {
      throw new Error('MySQL port must be between 1 and 65535');
    }
    if (!config.mysql.user) {
      throw new Error('MySQL user is required');
    }

    // 验证安全配置
    if (config.security.maxQueryLength <= 0) {
      throw new Error('maxQueryLength must be positive');
    }
    if (config.security.maxResultRows <= 0) {
      throw new Error('maxResultRows must be positive');
    }
    if (config.security.queryTimeout <= 0) {
      throw new Error('queryTimeout must be positive');
    }
    if (config.security.rateLimitMax <= 0) {
      throw new Error('rateLimitMax must be positive');
    }
    if (config.security.maxInputLength <= 0) {
      throw new Error('maxInputLength must be positive');
    }
  }
} 