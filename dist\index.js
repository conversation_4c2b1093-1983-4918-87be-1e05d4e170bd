#!/usr/bin/env node
// MySQL MCP服务器 - 为Model Context Protocol提供MySQL数据库操作功能
// 支持安全的数据库查询、更新、插入和删除操作
// 包含频率限制、输入验证、SQL注入防护等安全功能
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import mysql from 'mysql2/promise';
import crypto from 'crypto';
/**
 * MySQL MCP服务器类
 * 提供安全的MySQL数据库操作功能，包括：
 * - 数据查询、插入、更新、删除
 * - 表结构查询和管理
 * - 索引和外键信息查询
 * - 安全控制：频率限制、输入验证、SQL注入防护
 */
class MySQLMCPServer {
    server;
    pool = null;
    config;
    securityConfig;
    rateLimitMap = new Map();
    sessionId;
    /**
     * 初始化MCP服务器和配置
     */
    constructor() {
        // 生成唯一会话标识符，用于追踪和日志记录
        this.sessionId = crypto.randomUUID();
        // 初始化MCP服务器
        this.server = new Server({
            name: 'mysql-mcp-server',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {}, // 声明支持工具功能
            },
        });
        // 从环境变量中加载MySQL配置
        this.config = {
            host: process.env.MYSQL_HOST || 'localhost',
            port: parseInt(process.env.MYSQL_PORT || '3306'),
            user: process.env.MYSQL_USER || 'root',
            password: process.env.MYSQL_PASSWORD || '',
            database: process.env.MYSQL_DATABASE || '',
            connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10'),
            connectTimeout: parseInt(process.env.MYSQL_CONNECT_TIMEOUT || '60000'),
            idleTimeout: parseInt(process.env.MYSQL_IDLE_TIMEOUT || '60000'),
        };
        // 加载安全配置，设置防护措施
        this.securityConfig = {
            maxQueryLength: parseInt(process.env.MAX_QUERY_LENGTH || '10000'), // 最大查询长度
            allowedQueryTypes: (process.env.ALLOWED_QUERY_TYPES || 'SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER').split(','), // 允许的查询类型
            maxResultRows: parseInt(process.env.MAX_RESULT_ROWS || '1000'), // 最大返回行数
            queryTimeout: parseInt(process.env.QUERY_TIMEOUT || '30000'), // 查询超时时间
            rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '60000'), // 频率限制窗口（1分钟）
            rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'), // 每分钟最大请求数
        };
        // 设置请求处理器
        this.setupHandlers();
    }
    /**
     * 获取MySQL数据库连接
     * 使用连接池来管理数据库连接，提高性能和稳定性
     * @returns Promise<mysql.PoolConnection> 数据库连接对象
     */
    async getConnection() {
        // 懒加载连接池，只在首次需要时创建
        if (!this.pool) {
            const poolConfig = {
                host: this.config.host,
                port: this.config.port,
                user: this.config.user,
                password: this.config.password,
                database: this.config.database,
            };
            // 有条件地添加可选配置项，遵守TypeScript严格类型检查
            if (this.config.connectionLimit !== undefined) {
                poolConfig.connectionLimit = this.config.connectionLimit;
            }
            if (this.config.connectTimeout !== undefined) {
                poolConfig.connectTimeout = this.config.connectTimeout;
            }
            if (this.config.idleTimeout !== undefined) {
                poolConfig.idleTimeout = this.config.idleTimeout;
            }
            // 只在明确启用SSL时才添加SSL配置
            if (process.env.MYSQL_SSL === 'true') {
                poolConfig.ssl = { rejectUnauthorized: false };
            }
            this.pool = mysql.createPool(poolConfig);
        }
        return this.pool.getConnection();
    }
    /**
     * 验证用户输入的安全性
     * @param input 用户输入的数据
     * @param fieldName 字段名称，用于错误消息
     */
    validateInput(input, fieldName) {
        if (typeof input === 'string') {
            // 检查空字节注入攻击
            if (input.includes('\0')) {
                throw new Error(`Invalid character in ${fieldName}`);
            }
            // 检查输入长度，防止过长输入导致的攻击
            if (input.length > 1000) {
                throw new Error(`${fieldName} exceeds maximum length`);
            }
        }
    }
    /**
     * 验证SQL查询的安全性，防止SQL注入和危险操作
     * @param query SQL查询语句
     */
    validateQuery(query) {
        // 检查查询长度，防止过长查询导致的DoS攻击
        if (query.length > this.securityConfig.maxQueryLength) {
            throw new Error('Query exceeds maximum allowed length');
        }
        // 检查危险操作模式，防止文件操作和系统命令执行
        const dangerousPatterns = [
            /\b(LOAD_FILE|INTO OUTFILE|INTO DUMPFILE)\b/i, // 文件操作
            /\b(SYSTEM|EXEC|SHELL)\b/i, // 系统命令执行
            /\bINTO\s+OUTFILE\b/i, // 文件输出
            /\bLOAD\s+DATA\b/i, // 数据加载
        ];
        for (const pattern of dangerousPatterns) {
            if (pattern.test(query)) {
                throw new Error('Query contains prohibited operations');
            }
        }
        // 验证查询类型，只允许白名单中的操作
        const queryType = query.trim().split(/\s+/)[0]?.toUpperCase();
        if (queryType && !this.securityConfig.allowedQueryTypes.includes(queryType)) {
            throw new Error(`Query type '${queryType}' is not allowed`);
        }
    }
    /**
     * 验证表名的合法性，防止SQL注入
     * @param tableName 表名
     */
    validateTableName(tableName) {
        // 只允许字母、数字、下划线和短横线，防止SQL注入
        if (!/^[a-zA-Z0-9_-]+$/.test(tableName)) {
            throw new Error('Invalid table name format');
        }
        // 检查表名长度，MySQL表名最大长度为64字符
        if (tableName.length > 64) {
            throw new Error('Table name exceeds maximum length');
        }
    }
    /**
     * 检查请求频率限制，防止恶意请求和DoS攻击
     * @param identifier 请求标识符，用于区分不同的请求源
     */
    checkRateLimit(identifier = 'default') {
        const now = Date.now();
        const entry = this.rateLimitMap.get(identifier);
        // 如果没有记录或时间窗口已过期，重置计数器
        if (!entry || now > entry.resetTime) {
            this.rateLimitMap.set(identifier, {
                count: 1,
                resetTime: now + this.securityConfig.rateLimitWindow
            });
            return;
        }
        // 检查是否超过频率限制
        if (entry.count >= this.securityConfig.rateLimitMax) {
            throw new Error('Rate limit exceeded. Please try again later.');
        }
        // 增加请求计数
        entry.count++;
    }
    /**
     * 清理错误信息中的敏感信息，防止信息泄露
     * @param error 原始错误对象
     * @returns string 清理后的错误信息
     */
    sanitizeError(error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        // 移除错误信息中的敏感信息（密码、主机、用户名）
        const sanitizedMessage = errorMessage
            .replace(/password[^\s]*/gi, 'password=***')
            .replace(/host[^\s]*/gi, 'host=***')
            .replace(/user[^\s]*/gi, 'user=***');
        return sanitizedMessage;
    }
    /**
     * 记录安全事件，用于安全监控和审计
     * @param event 事件类型
     * @param details 事件详情
     */
    logSecurityEvent(event, details) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            sessionId: this.sessionId, // 会话标识符，用于追踪同一会话的操作
            event,
            details: typeof details === 'object' ? JSON.stringify(details) : details,
        };
        // 使用stderr输出安全日志，与普通日志区分
        console.error('[SECURITY]', JSON.stringify(logEntry));
    }
    /**
     * 设置请求处理器，处理MCP工具列表和工具调用
     */
    setupHandlers() {
        // 处理工具列表请求，返回所有可用的MySQL操作工具
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'mysql_query',
                        description: 'Execute a MySQL query (SELECT, SHOW, DESCRIBE, etc.)',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    description: 'The SQL query to execute',
                                },
                                params: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Optional parameters for prepared statements',
                                    default: [],
                                },
                            },
                            required: ['query'],
                        },
                    },
                    {
                        name: 'mysql_show_tables',
                        description: 'Show all tables in the current database',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_describe_table',
                        description: 'Describe the structure of a specific table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to describe',
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                    {
                        name: 'mysql_select_data',
                        description: 'Select data from a table with optional conditions',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to select from',
                                },
                                columns: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Columns to select (default: all columns)',
                                    default: ['*'],
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'Optional WHERE clause (without WHERE keyword)',
                                },
                                limit: {
                                    type: 'number',
                                    description: 'Optional LIMIT for results',
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                    {
                        name: 'mysql_insert_data',
                        description: 'Insert new data into a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to insert into',
                                },
                                data: {
                                    type: 'object',
                                    description: 'Key-value pairs of column names and values to insert',
                                },
                            },
                            required: ['table_name', 'data'],
                        },
                    },
                    {
                        name: 'mysql_update_data',
                        description: 'Update existing data in a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to update',
                                },
                                data: {
                                    type: 'object',
                                    description: 'Key-value pairs of column names and new values',
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'WHERE clause to specify which records to update (without WHERE keyword)',
                                },
                            },
                            required: ['table_name', 'data', 'where_clause'],
                        },
                    },
                    {
                        name: 'mysql_delete_data',
                        description: 'Delete data from a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to delete from',
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'WHERE clause to specify which records to delete (without WHERE keyword)',
                                },
                            },
                            required: ['table_name', 'where_clause'],
                        },
                    },
                    {
                        name: 'mysql_get_schema',
                        description: 'Get database schema information including tables, columns, and constraints',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get schema for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_get_indexes',
                        description: 'Get index information for a table or all tables',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get indexes for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_get_foreign_keys',
                        description: 'Get foreign key constraints for a table or all tables',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get foreign keys for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_create_table',
                        description: 'Create a new table with specified columns and constraints',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to create',
                                },
                                columns: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            name: { type: 'string' },
                                            type: { type: 'string' },
                                            nullable: { type: 'boolean', default: true },
                                            default: { type: 'string' },
                                            primary_key: { type: 'boolean', default: false },
                                            auto_increment: { type: 'boolean', default: false },
                                        },
                                        required: ['name', 'type'],
                                    },
                                    description: 'Array of column definitions',
                                },
                            },
                            required: ['table_name', 'columns'],
                        },
                    },
                    {
                        name: 'mysql_drop_table',
                        description: 'Drop (delete) a table from the database',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to drop',
                                },
                                if_exists: {
                                    type: 'boolean',
                                    description: 'Use IF EXISTS clause to avoid errors if table does not exist',
                                    default: true,
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                ],
            };
        });
        // 处理工具调用请求，执行具体的MySQL操作
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            let connection = null;
            try {
                // 检查请求频率限制，防止滥用
                this.checkRateLimit();
                // 验证输入参数的安全性
                if (args) {
                    Object.entries(args).forEach(([key, value]) => {
                        this.validateInput(value, key);
                    });
                }
                // 获取数据库连接
                connection = await this.getConnection();
                // 根据工具名称执行相应操作
                switch (name) {
                    case 'mysql_query': {
                        const { query, params = [] } = args;
                        // 安全性验证：检查SQL查询的合法性
                        this.validateQuery(query);
                        // 带超时控制的查询执行
                        const [rows] = await Promise.race([
                            connection.execute(query, params),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Query timeout')), this.securityConfig.queryTimeout))
                        ]);
                        // 限制返回结果的数量，防止内存滥用
                        const limitedRows = Array.isArray(rows) ? rows.slice(0, this.securityConfig.maxResultRows) : rows;
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(limitedRows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_show_tables': {
                        const [rows] = await Promise.race([
                            connection.execute('SHOW TABLES'),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Query timeout')), this.securityConfig.queryTimeout))
                        ]);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_describe_table': {
                        const { table_name } = args;
                        this.validateTableName(table_name);
                        const [rows] = await Promise.race([
                            connection.execute(`DESCRIBE \`${table_name}\``),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Query timeout')), this.securityConfig.queryTimeout))
                        ]);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_select_data': {
                        const { table_name, columns = ['*'], where_clause, limit } = args;
                        let query = `SELECT ${columns.join(', ')} FROM \`${table_name}\``;
                        if (where_clause) {
                            query += ` WHERE ${where_clause}`;
                        }
                        if (limit) {
                            query += ` LIMIT ${limit}`;
                        }
                        const [rows] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_insert_data': {
                        const { table_name, data } = args;
                        const columns = Object.keys(data);
                        const values = Object.values(data);
                        const placeholders = new Array(values.length).fill('?').join(', ');
                        const query = `INSERT INTO \`${table_name}\` (\`${columns.join('`, `')}\`) VALUES (${placeholders})`;
                        const [result] = await connection.execute(query, values);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify({ success: true, result }, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_update_data': {
                        const { table_name, data, where_clause } = args;
                        const columns = Object.keys(data);
                        const values = Object.values(data);
                        const setClause = columns.map(col => `\`${col}\` = ?`).join(', ');
                        const query = `UPDATE \`${table_name}\` SET ${setClause} WHERE ${where_clause}`;
                        const [result] = await connection.execute(query, values);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify({ success: true, result }, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_delete_data': {
                        const { table_name, where_clause } = args;
                        const query = `DELETE FROM \`${table_name}\` WHERE ${where_clause}`;
                        const [result] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify({ success: true, result }, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_get_schema': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
              FROM INFORMATION_SCHEMA.COLUMNS 
              WHERE TABLE_SCHEMA = DATABASE()
            `;
                        if (table_name) {
                            query += ` AND TABLE_NAME = '${table_name}'`;
                        }
                        query += ' ORDER BY TABLE_NAME, ORDINAL_POSITION';
                        const [rows] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_get_indexes': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                INDEX_NAME,
                COLUMN_NAME,
                NON_UNIQUE,
                SEQ_IN_INDEX,
                INDEX_TYPE
              FROM INFORMATION_SCHEMA.STATISTICS 
              WHERE TABLE_SCHEMA = DATABASE()
            `;
                        if (table_name) {
                            query += ` AND TABLE_NAME = '${table_name}'`;
                        }
                        query += ' ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX';
                        const [rows] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_get_foreign_keys': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME,
                UPDATE_RULE,
                DELETE_RULE
              FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
              WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            `;
                        if (table_name) {
                            query += ` AND TABLE_NAME = '${table_name}'`;
                        }
                        query += ' ORDER BY TABLE_NAME, CONSTRAINT_NAME';
                        const [rows] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify(rows, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_create_table': {
                        const { table_name, columns } = args;
                        let columnDefs = columns.map(col => {
                            let def = `\`${col.name}\` ${col.type}`;
                            if (!col.nullable)
                                def += ' NOT NULL';
                            if (col.auto_increment)
                                def += ' AUTO_INCREMENT';
                            if (col.default)
                                def += ` DEFAULT ${col.default}`;
                            return def;
                        });
                        const primaryKeys = columns.filter(col => col.primary_key).map(col => col.name);
                        if (primaryKeys.length > 0) {
                            columnDefs.push(`PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
                        }
                        const query = `CREATE TABLE \`${table_name}\` (${columnDefs.join(', ')})`;
                        const [result] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify({ success: true, result }, null, 2),
                                },
                            ],
                        };
                    }
                    case 'mysql_drop_table': {
                        const { table_name, if_exists = true } = args;
                        const query = `DROP TABLE ${if_exists ? 'IF EXISTS' : ''} \`${table_name}\``;
                        const [result] = await connection.execute(query);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: JSON.stringify({ success: true, result }, null, 2),
                                },
                            ],
                        };
                    }
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                // 记录安全事件，用于安全监控
                this.logSecurityEvent('error', { tool: name, error: error instanceof Error ? error.message : String(error) });
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error: ${this.sanitizeError(error)}`,
                        },
                    ],
                    isError: true,
                };
            }
            finally {
                // 始终释放连接回连接池，防止连接泄露
                if (connection) {
                    connection.release();
                }
            }
        });
    }
    /**
     * 启动MCP服务器，监听标准输入输出
     */
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('MySQL MCP Server running on stdio');
    }
    /**
     * 关闭mcp服务器，清理资源
     */
    async close() {
        // 关闭数据库连接池
        if (this.pool) {
            await this.pool.end();
            this.pool = null;
        }
        // 清理频率限制缓存
        this.rateLimitMap.clear();
        // 记录服务器关闭事件
        this.logSecurityEvent('server_shutdown', { sessionId: this.sessionId });
    }
}
// MySQL MCP服务器实例，提供MySQL数据库操作功能
const server = new MySQLMCPServer();
// 监听中断信号，优雅关闭服务器
process.on('SIGINT', async () => {
    await server.close();
    process.exit(0);
});
// 启动服务器，如果出现错误则输出到控制台
server.run().catch(console.error);
//# sourceMappingURL=index.js.map